<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get POST data
$input = json_decode(file_get_contents('php://input'), true);

// Validate required fields
$required_fields = ['name', 'email', 'service', 'message'];
foreach ($required_fields as $field) {
    if (empty($input[$field])) {
        http_response_code(400);
        echo json_encode(['error' => "Le champ $field est requis"]);
        exit;
    }
}

// Sanitize input
$name = htmlspecialchars(trim($input['name']));
$email = filter_var(trim($input['email']), FILTER_VALIDATE_EMAIL);
$service = htmlspecialchars(trim($input['service']));
$message = htmlspecialchars(trim($input['message']));

if (!$email) {
    http_response_code(400);
    echo json_encode(['error' => 'Email invalide']);
    exit;
}

// Email configuration
$to = '<EMAIL>'; // Remplacez par votre email
$subject = 'Nouvelle demande de contact - UGC Content Pro';

// Email content
$email_content = "
Nouvelle demande de contact depuis le site web UGC Content Pro

Nom: $name
Email: $email
Service demandé: $service

Message:
$message

---
Envoyé le: " . date('Y-m-d H:i:s') . "
IP: " . $_SERVER['REMOTE_ADDR'] . "
";

// Email headers
$headers = [
    'From: <EMAIL>',
    'Reply-To: ' . $email,
    'X-Mailer: PHP/' . phpversion(),
    'Content-Type: text/plain; charset=UTF-8'
];

// Send email
if (mail($to, $subject, $email_content, implode("\r\n", $headers))) {
    // Log the contact (optional)
    $log_entry = date('Y-m-d H:i:s') . " - Contact from: $name ($email) - Service: $service\n";
    file_put_contents('contacts.log', $log_entry, FILE_APPEND | LOCK_EX);
    
    echo json_encode([
        'success' => true,
        'message' => 'Message envoyé avec succès!'
    ]);
} else {
    http_response_code(500);
    echo json_encode([
        'error' => 'Erreur lors de l\'envoi du message'
    ]);
}
?>
