// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Mobile menu toggle
const menuBtn = document.getElementById('menuBtn');
const mobileMenu = document.createElement('div');
mobileMenu.className = 'md:hidden bg-white shadow-lg absolute top-full left-0 w-full';
mobileMenu.innerHTML = `
    <div class="px-4 py-2 space-y-2">
        <a href="#services" class="block py-2 text-gray-600 hover:text-purple-600">Services</a>
        <a href="#portfolio" class="block py-2 text-gray-600 hover:text-purple-600">Portfolio</a>
        <a href="#contact" class="block py-2 text-gray-600 hover:text-purple-600">Contact</a>
    </div>
`;

let mobileMenuOpen = false;

menuBtn.addEventListener('click', () => {
    if (!mobileMenuOpen) {
        menuBtn.parentElement.parentElement.appendChild(mobileMenu);
        mobileMenuOpen = true;
    } else {
        if (mobileMenu.parentElement) {
            mobileMenu.parentElement.removeChild(mobileMenu);
        }
        mobileMenuOpen = false;
    }
});

// Close mobile menu when clicking outside
document.addEventListener('click', (e) => {
    if (mobileMenuOpen && !menuBtn.contains(e.target) && !mobileMenu.contains(e.target)) {
        if (mobileMenu.parentElement) {
            mobileMenu.parentElement.removeChild(mobileMenu);
        }
        mobileMenuOpen = false;
    }
});

// Contact Modal Functions
function openContactModal() {
    document.getElementById('contactModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeContactModal() {
    document.getElementById('contactModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Close modal when clicking outside
document.getElementById('contactModal').addEventListener('click', (e) => {
    if (e.target.id === 'contactModal') {
        closeContactModal();
    }
});

// Contact Form Handling
document.getElementById('contactForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const name = document.getElementById('name').value;
    const email = document.getElementById('email').value;
    const service = document.getElementById('service').value;
    const message = document.getElementById('message').value;
    
    // Create WhatsApp message
    const whatsappMessage = `Bonjour! Je suis ${name} (${email}).
    
Je suis intéressé(e) par: ${service}

Message: ${message}

Merci!`;
    
    const whatsappUrl = `https://wa.me/212XXXXXXXXX?text=${encodeURIComponent(whatsappMessage)}`;
    
    // Show success message
    showNotification('Message préparé! Vous allez être redirigé vers WhatsApp.', 'success');
    
    // Redirect to WhatsApp after a short delay
    setTimeout(() => {
        window.open(whatsappUrl, '_blank');
    }, 1500);
    
    // Reset form
    this.reset();
});

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white font-semibold transform translate-x-full transition-transform duration-300 ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 
        'bg-blue-500'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Slide in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Slide out and remove
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentElement) {
                notification.parentElement.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Scroll animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe all cards and sections for animation
document.addEventListener('DOMContentLoaded', () => {
    const animatedElements = document.querySelectorAll('.card-hover, section');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
});

// Navbar background change on scroll
window.addEventListener('scroll', () => {
    const navbar = document.querySelector('nav');
    if (window.scrollY > 50) {
        navbar.classList.add('bg-white', 'shadow-lg');
        navbar.classList.remove('bg-transparent');
    } else {
        navbar.classList.remove('bg-white', 'shadow-lg');
        navbar.classList.add('bg-transparent');
    }
});

// Portfolio item click handlers
document.querySelectorAll('#portfolio .card-hover').forEach(card => {
    card.addEventListener('click', () => {
        showNotification('Portfolio complet disponible sur demande!', 'info');
    });
});

// Service card click handlers
document.querySelectorAll('#services .card-hover').forEach(card => {
    card.addEventListener('click', () => {
        openContactModal();
    });
});

// Add loading animation for buttons
document.querySelectorAll('button, a[href^="mailto"], a[href^="https://wa.me"]').forEach(btn => {
    btn.addEventListener('click', function() {
        if (this.type !== 'submit') {
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Chargement...';
            this.disabled = true;
            
            setTimeout(() => {
                this.innerHTML = originalText;
                this.disabled = false;
            }, 2000);
        }
    });
});

// Add typing effect to hero title
function typeWriter(element, text, speed = 100) {
    let i = 0;
    element.innerHTML = '';
    
    function type() {
        if (i < text.length) {
            element.innerHTML += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }
    
    type();
}

// Initialize typing effect when page loads
window.addEventListener('load', () => {
    const heroTitle = document.querySelector('h1');
    const originalText = heroTitle.textContent;
    typeWriter(heroTitle, originalText, 50);
});

// Add parallax effect to hero section
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const heroSection = document.querySelector('.gradient-bg');
    if (heroSection) {
        heroSection.style.transform = `translateY(${scrolled * 0.5}px)`;
    }
});

// Add counter animation for stats (if you want to add stats later)
function animateCounter(element, target, duration = 2000) {
    let start = 0;
    const increment = target / (duration / 16);
    
    function updateCounter() {
        start += increment;
        if (start < target) {
            element.textContent = Math.floor(start);
            requestAnimationFrame(updateCounter);
        } else {
            element.textContent = target;
        }
    }
    
    updateCounter();
}

// Email validation
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

// Enhanced form validation
document.getElementById('contactForm').addEventListener('input', function(e) {
    const field = e.target;
    const value = field.value.trim();
    
    // Remove existing error styling
    field.classList.remove('border-red-500');
    
    // Validate based on field type
    if (field.type === 'email' && value && !validateEmail(value)) {
        field.classList.add('border-red-500');
    } else if (field.required && !value) {
        field.classList.add('border-red-500');
    } else {
        field.classList.remove('border-red-500');
        field.classList.add('border-green-500');
    }
});

// Add social media sharing functionality
function shareOnSocial(platform, url = window.location.href, text = 'Découvrez UGC Content Pro - Services professionnels de création de contenu!') {
    const encodedUrl = encodeURIComponent(url);
    const encodedText = encodeURIComponent(text);
    
    const shareUrls = {
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
        twitter: `https://twitter.com/intent/tweet?url=${encodedUrl}&text=${encodedText}`,
        linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
        whatsapp: `https://wa.me/?text=${encodedText} ${encodedUrl}`
    };
    
    if (shareUrls[platform]) {
        window.open(shareUrls[platform], '_blank', 'width=600,height=400');
    }
}

console.log('UGC Content Pro website loaded successfully! 🚀');
