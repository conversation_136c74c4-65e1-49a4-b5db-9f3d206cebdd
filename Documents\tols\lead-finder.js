// Lead Finder JavaScript
let currentLeads = [];
let selectedLeads = [];

// Sample data for demonstration - In real implementation, this would come from APIs
const sampleLeads = [
    {
        id: 1,
        name: "Ahmed Marketing",
        platform: "Facebook",
        description: "Cherche créateur de contenu pour campagne TikTok",
        phone: "+212 6XX XXX XXX",
        whatsapp: true,
        budget: "2000-5000 DH",
        location: "Casablanca",
        posted: "Il y a 2 heures",
        quality: 85
    },
    {
        id: 2,
        name: "Boutique Mode Rabat",
        platform: "Instagram",
        description: "Besoin influenceur pour promotion produits mode",
        phone: "+212 7XX XXX XXX",
        whatsapp: true,
        budget: "1500-3000 DH",
        location: "Rabat",
        posted: "Il y a 5 heures",
        quality: 92
    },
    {
        id: 3,
        name: "Restaurant Casa",
        platform: "Avito",
        description: "Recherche vidéaste pour contenu food",
        phone: "+212 5XX XXX XXX",
        whatsapp: false,
        budget: "800-1500 DH",
        location: "Casablanca",
        posted: "Il y a 1 jour",
        quality: 78
    },
    {
        id: 4,
        name: "Startup Tech",
        platform: "LinkedIn",
        description: "UGC creator needed for app promotion",
        phone: "+212 6XX XXX XXX",
        whatsapp: true,
        budget: "3000-8000 DH",
        location: "Marrakech",
        posted: "Il y a 3 heures",
        quality: 95
    },
    {
        id: 5,
        name: "Agence Immobilier",
        platform: "Facebook",
        description: "Montage vidéo pour visites virtuelles",
        phone: "+212 7XX XXX XXX",
        whatsapp: true,
        budget: "1000-2500 DH",
        location: "Tanger",
        posted: "Il y a 6 heures",
        quality: 88
    }
];

// Search function
async function searchLeads() {
    const keywords = document.getElementById('keywords').value;
    const platform = document.getElementById('platform').value;
    const location = document.getElementById('location').value;
    const minBudget = document.getElementById('minBudget').value;
    const maxResults = parseInt(document.getElementById('maxResults').value);

    if (!keywords.trim()) {
        showNotification('Veuillez entrer des mots-clés de recherche', 'error');
        return;
    }

    // Show loading
    showLoading(true);
    document.getElementById('resultsSection').classList.remove('hidden');
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Filter sample data based on search criteria
    let filteredLeads = sampleLeads.filter(lead => {
        const matchesKeywords = keywords.toLowerCase().split(',').some(keyword => 
            lead.description.toLowerCase().includes(keyword.trim()) || 
            lead.name.toLowerCase().includes(keyword.trim())
        );
        
        const matchesPlatform = platform === 'all' || lead.platform.toLowerCase().includes(platform);
        const matchesLocation = !location || lead.location.toLowerCase().includes(location.toLowerCase());
        
        return matchesKeywords && matchesPlatform && matchesLocation;
    });

    // Limit results
    filteredLeads = filteredLeads.slice(0, maxResults);
    
    // Add some random generated leads for demo
    for (let i = filteredLeads.length; i < Math.min(maxResults, 15); i++) {
        filteredLeads.push(generateRandomLead(i + 10, keywords, platform, location));
    }

    currentLeads = filteredLeads;
    displayResults(filteredLeads);
    updateStats(filteredLeads);
    showLoading(false);
    
    showNotification(`${filteredLeads.length} prospects trouvés !`, 'success');
}

// Generate random lead for demo
function generateRandomLead(id, keywords, platform, location) {
    const names = ['Digital Agency', 'E-commerce Store', 'Local Business', 'Content Creator', 'Marketing Team'];
    const platforms = ['Facebook', 'Instagram', 'TikTok', 'LinkedIn', 'Avito'];
    const cities = ['Casablanca', 'Rabat', 'Marrakech', 'Fès', 'Tanger', 'Agadir'];
    const budgets = ['500-1000 DH', '1000-2500 DH', '2500-5000 DH', '5000+ DH'];
    
    return {
        id: id,
        name: names[Math.floor(Math.random() * names.length)] + ' ' + id,
        platform: platform === 'all' ? platforms[Math.floor(Math.random() * platforms.length)] : platform,
        description: `Recherche services ${keywords.split(',')[0]} pour projet marketing`,
        phone: `+212 ${Math.floor(Math.random() * 3) + 5}XX XXX XXX`,
        whatsapp: Math.random() > 0.3,
        budget: budgets[Math.floor(Math.random() * budgets.length)],
        location: location || cities[Math.floor(Math.random() * cities.length)],
        posted: `Il y a ${Math.floor(Math.random() * 24)} heures`,
        quality: Math.floor(Math.random() * 30) + 70
    };
}

// Display results
function displayResults(leads) {
    const container = document.getElementById('resultsContainer');
    container.innerHTML = '';

    leads.forEach(lead => {
        const leadCard = createLeadCard(lead);
        container.appendChild(leadCard);
    });
}

// Create lead card
function createLeadCard(lead) {
    const card = document.createElement('div');
    card.className = 'result-card bg-gray-50 rounded-lg p-6 border border-gray-200';
    
    const qualityColor = lead.quality >= 90 ? 'text-green-600' : lead.quality >= 80 ? 'text-yellow-600' : 'text-red-600';
    const whatsappIcon = lead.whatsapp ? '<i class="fab fa-whatsapp text-green-500 ml-2"></i>' : '';
    
    card.innerHTML = `
        <div class="flex items-start justify-between mb-4">
            <div class="flex-1">
                <div class="flex items-center mb-2">
                    <h4 class="text-lg font-bold text-gray-800">${lead.name}</h4>
                    ${whatsappIcon}
                    <span class="ml-auto text-sm ${qualityColor} font-semibold">${lead.quality}% qualité</span>
                </div>
                <p class="text-gray-600 mb-2">${lead.description}</p>
                <div class="flex items-center text-sm text-gray-500 space-x-4">
                    <span><i class="fas fa-map-marker-alt mr-1"></i>${lead.location}</span>
                    <span><i class="fas fa-clock mr-1"></i>${lead.posted}</span>
                    <span><i class="fas fa-money-bill mr-1"></i>${lead.budget}</span>
                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded">${lead.platform}</span>
                </div>
            </div>
        </div>
        
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <input type="checkbox" id="lead-${lead.id}" onchange="toggleLeadSelection(${lead.id})" 
                       class="w-4 h-4 text-purple-600 border-gray-300 rounded focus:ring-purple-500">
                <label for="lead-${lead.id}" class="text-sm text-gray-600">Sélectionner</label>
            </div>
            
            <div class="flex space-x-2">
                <button onclick="viewLeadDetails(${lead.id})" 
                        class="bg-blue-100 text-blue-800 px-3 py-1 rounded text-sm hover:bg-blue-200 transition">
                    <i class="fas fa-eye mr-1"></i>Détails
                </button>
                <button onclick="contactLead(${lead.id})" 
                        class="bg-green-100 text-green-800 px-3 py-1 rounded text-sm hover:bg-green-200 transition">
                    <i class="fab fa-whatsapp mr-1"></i>Contacter
                </button>
                <button onclick="saveToFavorites(${lead.id})" 
                        class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded text-sm hover:bg-yellow-200 transition">
                    <i class="fas fa-star mr-1"></i>Favoris
                </button>
            </div>
        </div>
    `;
    
    return card;
}

// Quick search function
function quickSearch(keywords) {
    document.getElementById('keywords').value = keywords;
    searchLeads();
}

// Contact individual lead
function contactLead(leadId) {
    const lead = currentLeads.find(l => l.id === leadId);
    if (!lead) return;

    const message = `Bonjour ${lead.name} ! 👋

J'ai vu votre annonce concernant : "${lead.description}"

Je suis spécialisé dans la création de contenu UGC professionnel et je pense pouvoir vous aider avec votre projet.

🎬 Mes services incluent :
• Montage vidéo TikTok/Instagram/Facebook
• Voix-off professionnelle
• Création de contenu engageant
• Optimisation pour chaque plateforme

Seriez-vous intéressé(e) par une collaboration ? Je peux vous envoyer mon portfolio et discuter de votre projet.

Cordialement !`;

    const whatsappUrl = `https://wa.me/${lead.phone.replace(/\s/g, '')}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
    
    showNotification(`Message envoyé à ${lead.name}`, 'success');
}

// Toggle lead selection
function toggleLeadSelection(leadId) {
    const index = selectedLeads.indexOf(leadId);
    if (index > -1) {
        selectedLeads.splice(index, 1);
    } else {
        selectedLeads.push(leadId);
    }
    
    updateSelectionCount();
}

// Update selection count
function updateSelectionCount() {
    const count = selectedLeads.length;
    // Update UI to show selection count if needed
}

// Send bulk WhatsApp messages
function sendBulkWhatsApp() {
    if (selectedLeads.length === 0) {
        showNotification('Veuillez sélectionner au moins un prospect', 'error');
        return;
    }
    
    document.getElementById('messageModal').classList.remove('hidden');
}

// Send custom message
function sendCustomMessage() {
    const message = document.getElementById('messageTemplate').value;
    if (!message.trim()) {
        showNotification('Veuillez entrer un message', 'error');
        return;
    }

    selectedLeads.forEach(leadId => {
        const lead = currentLeads.find(l => l.id === leadId);
        if (lead && lead.whatsapp) {
            const personalizedMessage = message.replace('[Nom du prospect]', lead.name);
            const whatsappUrl = `https://wa.me/${lead.phone.replace(/\s/g, '')}?text=${encodeURIComponent(personalizedMessage)}`;
            
            // Open with delay to avoid spam detection
            setTimeout(() => {
                window.open(whatsappUrl, '_blank');
            }, leadId * 1000);
        }
    });

    closeMessageModal();
    showNotification(`Messages envoyés à ${selectedLeads.length} prospects`, 'success');
    selectedLeads = [];
    
    // Uncheck all checkboxes
    document.querySelectorAll('input[type="checkbox"]').forEach(cb => cb.checked = false);
}

// Close message modal
function closeMessageModal() {
    document.getElementById('messageModal').classList.add('hidden');
}

// Export results to CSV
function exportResults() {
    if (currentLeads.length === 0) {
        showNotification('Aucun résultat à exporter', 'error');
        return;
    }

    const csvContent = generateCSV(currentLeads);
    downloadCSV(csvContent, 'prospects-ugc.csv');
    showNotification('Fichier CSV téléchargé !', 'success');
}

// Generate CSV content
function generateCSV(leads) {
    const headers = ['Nom', 'Plateforme', 'Description', 'Téléphone', 'WhatsApp', 'Budget', 'Localisation', 'Publié', 'Qualité'];
    const rows = leads.map(lead => [
        lead.name,
        lead.platform,
        lead.description,
        lead.phone,
        lead.whatsapp ? 'Oui' : 'Non',
        lead.budget,
        lead.location,
        lead.posted,
        lead.quality + '%'
    ]);

    return [headers, ...rows].map(row => row.map(field => `"${field}"`).join(',')).join('\n');
}

// Download CSV file
function downloadCSV(content, filename) {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Update statistics
function updateStats(leads) {
    document.getElementById('statsSection').classList.remove('hidden');
    
    const totalLeads = leads.length;
    const withPhone = leads.filter(l => l.phone).length;
    const withWhatsApp = leads.filter(l => l.whatsapp).length;
    const avgQuality = Math.round(leads.reduce((sum, l) => sum + l.quality, 0) / totalLeads);

    document.getElementById('totalLeads').textContent = totalLeads;
    document.getElementById('withPhone').textContent = withPhone;
    document.getElementById('withWhatsApp').textContent = withWhatsApp;
    document.getElementById('qualityScore').textContent = avgQuality + '%';
}

// Show/hide loading indicator
function showLoading(show) {
    const indicator = document.getElementById('loadingIndicator');
    const container = document.getElementById('resultsContainer');
    
    if (show) {
        indicator.classList.remove('hidden');
        container.classList.add('hidden');
    } else {
        indicator.classList.add('hidden');
        container.classList.remove('hidden');
    }
}

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white font-semibold transform translate-x-full transition-transform duration-300 ${
        type === 'success' ? 'bg-green-500' : 
        type === 'error' ? 'bg-red-500' : 
        'bg-blue-500'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => notification.style.transform = 'translateX(0)', 100);
    
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// View lead details (placeholder)
function viewLeadDetails(leadId) {
    const lead = currentLeads.find(l => l.id === leadId);
    if (lead) {
        alert(`Détails de ${lead.name}:\n\nDescription: ${lead.description}\nTéléphone: ${lead.phone}\nBudget: ${lead.budget}\nLocalisation: ${lead.location}`);
    }
}

// Save to favorites (placeholder)
function saveToFavorites(leadId) {
    showNotification('Prospect ajouté aux favoris !', 'success');
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Add enter key support for search
    document.getElementById('keywords').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            searchLeads();
        }
    });
    
    console.log('Lead Finder UGC chargé avec succès ! 🎯');
});
