<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lead Finder - Trouveur de Contacts UGC</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .result-card {
            transition: all 0.3s ease;
        }
        .result-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        }
        .loading {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white py-6">
        <div class="max-w-6xl mx-auto px-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <i class="fas fa-search text-2xl mr-3"></i>
                    <h1 class="text-2xl font-bold">Lead Finder UGC</h1>
                </div>
                <a href="index.html" class="bg-white text-purple-600 px-4 py-2 rounded-lg font-semibold hover:bg-gray-100 transition">
                    <i class="fas fa-home mr-2"></i>Retour au Site
                </a>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="max-w-6xl mx-auto px-4 py-8">
        <!-- Search Section -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <h2 class="text-2xl font-bold mb-6 text-gray-800">
                <i class="fas fa-bullseye text-purple-600 mr-2"></i>
                Recherche de Prospects UGC
            </h2>
            
            <div class="grid md:grid-cols-2 gap-6 mb-6">
                <div>
                    <label class="block text-gray-700 font-semibold mb-2">Mots-clés de recherche</label>
                    <input type="text" id="keywords" placeholder="Ex: créateur contenu, influenceur, vidéo TikTok..." 
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-600 focus:border-transparent">
                    <p class="text-sm text-gray-500 mt-1">Séparez les mots-clés par des virgules</p>
                </div>
                
                <div>
                    <label class="block text-gray-700 font-semibold mb-2">Plateforme cible</label>
                    <select id="platform" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-600 focus:border-transparent">
                        <option value="all">Toutes les plateformes</option>
                        <option value="facebook">Facebook Marketplace</option>
                        <option value="instagram">Instagram</option>
                        <option value="tiktok">TikTok</option>
                        <option value="linkedin">LinkedIn</option>
                        <option value="avito">Avito</option>
                    </select>
                </div>
            </div>

            <div class="grid md:grid-cols-3 gap-4 mb-6">
                <div>
                    <label class="block text-gray-700 font-semibold mb-2">Ville/Région</label>
                    <input type="text" id="location" placeholder="Ex: Casablanca, Rabat..." 
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-600 focus:border-transparent">
                </div>
                
                <div>
                    <label class="block text-gray-700 font-semibold mb-2">Budget min (DH)</label>
                    <input type="number" id="minBudget" placeholder="500" 
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-600 focus:border-transparent">
                </div>
                
                <div>
                    <label class="block text-gray-700 font-semibold mb-2">Nombre de résultats</label>
                    <select id="maxResults" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-600 focus:border-transparent">
                        <option value="10">10 résultats</option>
                        <option value="25">25 résultats</option>
                        <option value="50">50 résultats</option>
                        <option value="100">100 résultats</option>
                    </select>
                </div>
            </div>

            <button onclick="searchLeads()" id="searchBtn" 
                    class="w-full bg-purple-600 text-white py-3 rounded-lg font-semibold hover:bg-purple-700 transition flex items-center justify-center">
                <i class="fas fa-search mr-2"></i>
                Rechercher des Prospects
            </button>
        </div>

        <!-- Quick Search Templates -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
            <h3 class="text-xl font-bold mb-4 text-gray-800">
                <i class="fas fa-lightning-bolt text-yellow-500 mr-2"></i>
                Recherches Rapides
            </h3>
            <div class="grid md:grid-cols-3 gap-4">
                <button onclick="quickSearch('créateur contenu, UGC, vidéo marketing')" 
                        class="bg-blue-100 text-blue-800 px-4 py-3 rounded-lg hover:bg-blue-200 transition">
                    <i class="fas fa-video mr-2"></i>Créateurs UGC
                </button>
                <button onclick="quickSearch('influenceur, collaboration, partenariat')" 
                        class="bg-green-100 text-green-800 px-4 py-3 rounded-lg hover:bg-green-200 transition">
                    <i class="fas fa-users mr-2"></i>Influenceurs
                </button>
                <button onclick="quickSearch('montage vidéo, editing, post-production')" 
                        class="bg-purple-100 text-purple-800 px-4 py-3 rounded-lg hover:bg-purple-200 transition">
                    <i class="fas fa-cut mr-2"></i>Services Vidéo
                </button>
            </div>
        </div>

        <!-- Results Section -->
        <div id="resultsSection" class="hidden">
            <div class="bg-white rounded-xl shadow-lg p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-bold text-gray-800">
                        <i class="fas fa-list mr-2"></i>
                        Résultats de Recherche
                    </h3>
                    <div class="flex space-x-2">
                        <button onclick="exportResults()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition">
                            <i class="fas fa-download mr-2"></i>Exporter CSV
                        </button>
                        <button onclick="sendBulkWhatsApp()" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition">
                            <i class="fab fa-whatsapp mr-2"></i>Message Groupé
                        </button>
                    </div>
                </div>
                
                <div id="resultsContainer" class="space-y-4">
                    <!-- Results will be populated here -->
                </div>
                
                <div id="loadingIndicator" class="text-center py-8 hidden">
                    <i class="fas fa-spinner loading text-4xl text-purple-600 mb-4"></i>
                    <p class="text-gray-600">Recherche en cours...</p>
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div id="statsSection" class="hidden mt-8">
            <div class="grid md:grid-cols-4 gap-6">
                <div class="bg-white rounded-xl shadow-lg p-6 text-center">
                    <i class="fas fa-users text-3xl text-blue-600 mb-2"></i>
                    <div class="text-2xl font-bold text-gray-800" id="totalLeads">0</div>
                    <div class="text-gray-600">Prospects Trouvés</div>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6 text-center">
                    <i class="fas fa-phone text-3xl text-green-600 mb-2"></i>
                    <div class="text-2xl font-bold text-gray-800" id="withPhone">0</div>
                    <div class="text-gray-600">Avec Téléphone</div>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6 text-center">
                    <i class="fab fa-whatsapp text-3xl text-green-500 mb-2"></i>
                    <div class="text-2xl font-bold text-gray-800" id="withWhatsApp">0</div>
                    <div class="text-gray-600">WhatsApp Détecté</div>
                </div>
                <div class="bg-white rounded-xl shadow-lg p-6 text-center">
                    <i class="fas fa-star text-3xl text-yellow-500 mb-2"></i>
                    <div class="text-2xl font-bold text-gray-800" id="qualityScore">0%</div>
                    <div class="text-gray-600">Score Qualité</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Message Template Modal -->
    <div id="messageModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="bg-white rounded-xl p-8 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
            <h3 class="text-2xl font-bold mb-4">Personnaliser le Message</h3>
            <textarea id="messageTemplate" rows="8" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-600 focus:border-transparent" 
                      placeholder="Votre message personnalisé...">Bonjour ! 👋

Je suis spécialisé dans la création de contenu UGC professionnel pour les réseaux sociaux.

🎬 Mes services :
• Montage vidéo TikTok/Instagram
• Voix-off professionnelle
• Création de contenu engageant

J'ai vu que vous cherchez des services de création de contenu. Je peux vous aider à booster votre présence en ligne avec du contenu qui convertit !

Êtes-vous intéressé(e) par une collaboration ?

Cordialement,
[Votre nom]</textarea>
            
            <div class="flex space-x-4 mt-6">
                <button onclick="sendCustomMessage()" class="flex-1 bg-green-500 text-white py-3 rounded-lg font-semibold hover:bg-green-600 transition">
                    <i class="fab fa-whatsapp mr-2"></i>Envoyer via WhatsApp
                </button>
                <button onclick="closeMessageModal()" class="flex-1 bg-gray-300 text-gray-700 py-3 rounded-lg font-semibold hover:bg-gray-400 transition">
                    Annuler
                </button>
            </div>
        </div>
    </div>

    <script src="lead-finder.js"></script>
</body>
</html>
