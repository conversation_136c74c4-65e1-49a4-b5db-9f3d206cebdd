# UGC Content Pro - Site Web de Services de Création de Contenu

Un site web professionnel pour promouvoir vos services de création de contenu UGC (User Generated Content), montage vidéo et voix-off pour les réseaux sociaux.

## 🚀 Fonctionnalités

- **Design Moderne et Responsive** - Compatible mobile, tablette et desktop
- **Interface Multilingue** - Optimisé pour le marché francophone
- **Intégration WhatsApp** - Contact direct via WhatsApp
- **Formulaire de Contact** - Avec validation et envoi par email
- **Animations Fluides** - Effets de scroll et transitions
- **SEO Optimisé** - Structure HTML sémantique

## 📁 Structure du Projet

```
tols/
├── index.html          # Page principale
├── script.js           # JavaScript pour l'interactivité
├── contact.php         # Gestionnaire de formulaire (optionnel)
└── README.md          # Ce fichier
```

## 🛠️ Installation et Configuration

### 1. Configuration de Base

1. **Personnalisez les informations de contact** dans `index.html` :
   - Remplacez `+212 6XX XXX XXX` par votre numéro WhatsApp
   - Remplacez `<EMAIL>` par votre email
   - Ajoutez vos liens de réseaux sociaux

2. **Configurez WhatsApp** dans `script.js` :
   ```javascript
   // Ligne 67 - Remplacez par votre numéro WhatsApp
   const whatsappUrl = `https://wa.me/212XXXXXXXXX?text=${encodeURIComponent(whatsappMessage)}`;
   ```

### 2. Déploiement Simple (HTML/CSS/JS uniquement)

Pour un déploiement simple sans backend :

1. **Hébergement gratuit** :
   - [Netlify](https://netlify.com) - Glissez-déposez le dossier
   - [Vercel](https://vercel.com) - Connectez votre repository GitHub
   - [GitHub Pages](https://pages.github.com) - Hébergement gratuit

2. **Étapes pour Netlify** :
   ```bash
   # 1. Créez un compte sur netlify.com
   # 2. Glissez-déposez le dossier tols/ sur netlify.com
   # 3. Votre site sera disponible avec une URL comme: https://random-name.netlify.app
   ```

### 3. Déploiement avec Backend PHP

Si vous voulez utiliser le formulaire de contact avec PHP :

1. **Hébergement avec PHP** :
   - [OVH](https://ovh.com)
   - [Hostinger](https://hostinger.com)
   - [000webhost](https://000webhost.com) (gratuit)

2. **Configuration du formulaire** :
   - Modifiez l'email de destination dans `contact.php` ligne 25
   - Uploadez tous les fichiers via FTP

## 🎨 Personnalisation

### Couleurs et Style

Les couleurs principales sont définies dans `index.html` :
- **Violet principal** : `#667eea` et `#764ba2`
- **Couleurs d'accent** : Modifiables via les classes Tailwind

### Contenu

1. **Services** : Modifiez la section services (ligne 70-120 dans index.html)
2. **Portfolio** : Ajoutez vos projets (ligne 140-180)
3. **Témoignages** : Ajoutez une section témoignages si nécessaire

### Images

Pour ajouter de vraies images :
1. Créez un dossier `images/`
2. Remplacez les placeholders par vos images
3. Optimisez les images pour le web (format WebP recommandé)

## 📱 Fonctionnalités de Contact

### WhatsApp Integration

Le site redirige automatiquement vers WhatsApp avec un message pré-rempli :
```
Bonjour! Je suis [NOM] ([EMAIL]).

Je suis intéressé(e) par: [SERVICE]

Message: [MESSAGE]

Merci!
```

### Email de Contact

Le formulaire peut envoyer des emails automatiquement si vous utilisez le backend PHP.

## 🔧 Développement Local

Pour tester localement :

1. **Serveur simple** :
   ```bash
   # Si vous avez Python installé
   python -m http.server 8000
   
   # Ou avec Node.js
   npx serve .
   ```

2. **Avec PHP** :
   ```bash
   php -S localhost:8000
   ```

3. Ouvrez `http://localhost:8000` dans votre navigateur

## 📈 Optimisations SEO

Le site inclut :
- Meta tags optimisés
- Structure HTML sémantique
- Temps de chargement rapide
- Design responsive

### Améliorations suggérées :

1. **Google Analytics** :
   ```html
   <!-- Ajoutez avant </head> -->
   <script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
   ```

2. **Meta tags supplémentaires** :
   ```html
   <meta name="description" content="Services professionnels de création de contenu UGC...">
   <meta property="og:title" content="UGC Content Pro">
   <meta property="og:description" content="...">
   ```

## 🚀 Prochaines Étapes

1. **Testez le site** localement
2. **Personnalisez** le contenu et les couleurs
3. **Ajoutez vos informations** de contact
4. **Déployez** sur votre hébergeur préféré
5. **Configurez** un nom de domaine personnalisé

## 📞 Support

Pour toute question sur l'utilisation ou la personnalisation de ce site, vous pouvez :
- Modifier le code selon vos besoins
- Ajouter de nouvelles fonctionnalités
- Intégrer avec d'autres services (calendly, stripe, etc.)

## 📄 Licence

Ce projet est libre d'utilisation pour vos projets commerciaux et personnels.

---

**Bonne chance avec votre business de création de contenu UGC ! 🎬✨**
